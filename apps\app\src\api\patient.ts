/**
 * 患者管理 API
 */

export interface Patient {
  id: string;
  name: string;
  age: number;
  gender: 'male' | 'female';
  phone?: string;
  address?: string;
  medicalHistory?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PatientQuery {
  page?: number;
  pageSize?: number;
  name?: string;
  phone?: string;
}

export interface PatientResponse {
  success: boolean;
  data?: Patient[];
  total?: number;
  message?: string;
}

// VTJ 期望的类型别名
export type PatientData = Patient;
export type PatientListParams = PatientQuery;
export type AddPatientParams = Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>;

/**
 * 获取患者列表
 */
export async function getPatientList(query: PatientQuery = {}): Promise<PatientResponse> {
  try {
    // 模拟 API 调用
    const mockPatients: Patient[] = [
      {
        id: '1',
        name: '张三',
        age: 35,
        gender: 'male',
        phone: '13800138001',
        address: '北京市朝阳区',
        medicalHistory: '高血压',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '2',
        name: '李四',
        age: 28,
        gender: 'female',
        phone: '13800138002',
        address: '上海市浦东新区',
        medicalHistory: '糖尿病',
        createdAt: '2024-01-02T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z'
      }
    ];

    return {
      success: true,
      data: mockPatients,
      total: mockPatients.length
    };
  } catch (error) {
    return {
      success: false,
      message: '获取患者列表失败'
    };
  }
}

/**
 * 获取患者详情
 */
export async function getPatientById(id: string): Promise<PatientResponse> {
  try {
    // 模拟 API 调用
    const mockPatient: Patient = {
      id,
      name: '张三',
      age: 35,
      gender: 'male',
      phone: '13800138001',
      address: '北京市朝阳区',
      medicalHistory: '高血压',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    };

    return {
      success: true,
      data: [mockPatient]
    };
  } catch (error) {
    return {
      success: false,
      message: '获取患者详情失败'
    };
  }
}

/**
 * 创建患者
 */
export async function createPatient(patient: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>): Promise<PatientResponse> {
  try {
    // 模拟 API 调用
    const newPatient: Patient = {
      ...patient,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      data: [newPatient],
      message: '创建患者成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '创建患者失败'
    };
  }
}

/**
 * 更新患者
 */
export async function updatePatient(id: string, patient: Partial<Patient>): Promise<PatientResponse> {
  try {
    // 模拟 API 调用
    const updatedPatient: Patient = {
      id,
      name: patient.name || '张三',
      age: patient.age || 35,
      gender: patient.gender || 'male',
      phone: patient.phone,
      address: patient.address,
      medicalHistory: patient.medicalHistory,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      data: [updatedPatient],
      message: '更新患者成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '更新患者失败'
    };
  }
}

/**
 * 删除患者
 */
export async function deletePatient(id: string): Promise<PatientResponse> {
  try {
    // 模拟 API 调用
    return {
      success: true,
      message: '删除患者成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '删除患者失败'
    };
  }
}
